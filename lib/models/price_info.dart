class PriceInfo {
  String originPrice;
  bool isNegative;
  List<String> priceParts;

  PriceInfo(this.originPrice, this.isNegative, this.priceParts);

  static PriceInfo? parsePrice(String price) {
    double dPrice = double.tryParse(price) ?? 0.0;
    parseDouble(dPrice);
    return PriceInfo(price, dPrice.isNegative, parseDouble(dPrice));
  }

  static List<String> parseDouble(double value) {
    String sign = value.isNegative ? "-" : "+";
    double absValue = value.abs();

    String valueStr = absValue.toString();
    List<String> parts = valueStr.split('.');

    String integerPart = parts[0];
    String fractionalPart = "0"; // Default if no fractional part

    if (parts.length > 1) {
      fractionalPart = parts[1];
    }

    return [sign, integerPart, fractionalPart];
  }
}
