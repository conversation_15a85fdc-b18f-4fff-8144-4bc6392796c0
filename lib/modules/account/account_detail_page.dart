import 'package:flutter/material.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/models/price_info.dart';
import 'package:qiazhun/modules/account/account_model.dart';
import 'package:qiazhun/modules/account/account_repo.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:qiazhun/widgets/empty_view.dart';
import 'package:qiazhun/widgets/price_view.dart';

class AccountDetailPage extends StatefulWidget {
  final String accountId;
  const AccountDetailPage({required this.accountId, super.key});

  @override
  State<StatefulWidget> createState() => _AccountDetailState();
}

class _AccountDetailState extends State<AccountDetailPage> {
  bool _isLoading = true;
  AccountDetailAndLogResp? _accountDetailData;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final response = await AccountRepo.getAccountDetailAndLog(widget.accountId);
      if (response.code == 1 && response.data != null) {
        _accountDetailData = response.data;
      } else {
        showToast(response.msg ?? '');
      }
    } catch (e) {
      showToast(e.toString());
    } finally {
      _isLoading = false;
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      body: SafeArea(
        top: false,
        child: Stack(
          children: [
            // App Bar
            Container(
              color: const Color(0xFFF5F5F5),
            ),
            Positioned(
              // top: 0,
              child: Container(
                height: 203,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                      colors: [Color(0xFFF5F5F5), Color(0xFFAEE7CD)], begin: Alignment.bottomCenter, end: Alignment.topCenter, tileMode: TileMode.clamp),
                ),
              ),
            ),
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: AppBar(
                backgroundColor: Colors.transparent,
                scrolledUnderElevation: 0,
                centerTitle: true,
                leading: IconButton(
                  icon: Image.asset(
                    'assets/images/ic_back.png',
                    width: 24,
                    height: 24,
                  ),
                  onPressed: () {
                    RouterHelper.router.pop();
                  },
                ),
                titleTextStyle: const TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                title: Text(
                  _getAccountTitle(),
                  style: const TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                ),
                actions: [
                  IconButton(
                    icon: const Icon(Icons.more_horiz, color: MColor.xFF1B1C1A),
                    onPressed: () {
                      // Handle menu action
                    },
                  ),
                ],
              ),
            ),
            // Content
            Positioned.fill(
              top: AppBar().preferredSize.height + MediaQuery.of(context).padding.top,
              child: RefreshIndicator(
                onRefresh: _loadData,
                child: _buildContent(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getAccountTitle() {
    if (_accountDetailData?.detail != null) {
      final detail = _accountDetailData!.detail;
      if (detail.cardType == '2') {
        return '${detail.bankName}信用卡';
      } else if (detail.cardType == '1') {
        return '${detail.bankName}储蓄卡';
      } else {
        return detail.accountName ?? '账户详情';
      }
    }
    return '账户详情';
  }

  Widget _buildContent() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          color: MColor.skin,
        ),
      );
    }

    if (_accountDetailData == null) {
      return const Center(
        child: EmptyView(),
      );
    }

    return ListView(
      padding: EdgeInsets.zero,
      children: [
        _buildAccountCard(),
        const SizedBox(height: 16),
        _buildActionButtons(),
        const SizedBox(height: 16),
        _buildMonthlyBill(),
        const SizedBox(height: 16),
        _buildTransactionList(),
      ],
    );
  }

  Widget _buildAccountCard() {
    final detail = _accountDetailData!.detail;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFFFED58E), Color(0xFFF6CB86)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Card debt amount
          if (detail.cardType == '2') ...[
            const Text(
              '剩余欠款',
              style: TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w400,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              detail.billAmount ?? '0.00',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 32,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            // Credit card details
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '总额度',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        detail.availableAmount ?? '0.00',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '制卡额度',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        detail.consumptionAmount ?? '0.00',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '出账日',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        detail.billDate?.isNotEmpty == true ? '每月${detail.billDate}号' : '未设置',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '还款日',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        detail.repaymentDate?.isNotEmpty == true ? '每月${detail.repaymentDate}号' : '未设置',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // Card number
            Row(
              children: [
                const Text(
                  '卡号',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  (detail.accountName?.length ?? 0) > 4 ? '****${detail.accountName!.substring(detail.accountName!.length - 4)}' : (detail.accountName ?? ''),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(width: 8),
                GestureDetector(
                  onTap: () {
                    // Copy card number functionality
                  },
                  child: const Icon(
                    Icons.copy,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
              ],
            ),
          ] else ...[
            // For other account types (savings, etc.)
            Text(
              '账户余额',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            PriceView(price: PriceInfo.parsePrice(detail.balance ?? '0.00'), integerFontSize: 34, fractionalFontSize: 20),
          ],
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    final detail = _accountDetailData!.detail;
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          if (detail.cardType == '2') ...[
            Expanded(
              flex: 1,
              child: _buildActionButton(
                icon: Icons.edit,
                label: '记账',
                onTap: () {
                  // Navigate to bookkeeping
                  RouterHelper.router.pushNamed('/bookkeeping');
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              flex: 1,
              child: _buildActionButton(
                icon: Icons.payment,
                label: '还款',
                onTap: () {
                  // Navigate to repayment
                },
              ),
            ),
          ] else ...[
            Expanded(
              flex: 2,
              child: _buildActionButton(
                icon: Icons.edit,
                label: '添加一条新记账',
                onTap: () {
                  // Navigate to bookkeeping
                  RouterHelper.router.pushNamed('/bookkeeping');
                },
              ),
            ),
            const SizedBox(width: 12),
          ],
          const SizedBox(width: 12),
          Expanded(
            flex: 1,
            child: _buildActionButton(
              icon: Icons.swap_horiz,
              label: '转账',
              onTap: () {
                // Navigate to transfer
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: MColor.skin,
              size: 16,
            ),
            const SizedBox(width: 8),
            Text(
              label,
              style: const TextStyle(
                color: MColor.xFF1B1C1A,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMonthlyBill() {
    final detail = _accountDetailData!.detail;

    // Only show for credit cards
    if (detail.cardType != '2') {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '待还${DateTime.now().year}年${DateTime.now().month}月账单',
                  style: const TextStyle(
                    color: MColor.xFF1B1C1A,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  detail.billAmount ?? '0.00',
                  style: const TextStyle(
                    color: MColor.xFF1B1C1A,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          GestureDetector(
            onTap: () {
              // Navigate to repayment
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: const Color(0xFFFED58E),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Text(
                '去还款',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionList() {
    final logs = _accountDetailData!.list;

    if (logs.isEmpty) {
      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 16),
        padding: const EdgeInsets.all(32),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Center(
          child: Text(
            '没有更多数据',
            style: TextStyle(
              color: MColor.xFF999999,
              fontSize: 14,
            ),
          ),
        ),
      );
    }

    return Column(
      children: logs.map((log) => _buildDateSection(log)).toList(),
    );
  }

  Widget _buildDateSection(AccountDateLog log) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        children: [
          // Date header
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                Text(
                  _formatDate(log.date),
                  style: const TextStyle(
                    color: MColor.xFF1B1C1A,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const Spacer(),
                Text(
                  '收 ${log.income}',
                  style: const TextStyle(
                    color: MColor.xFFCB322E,
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                const SizedBox(width: 16),
                Text(
                  '支 ${log.expense}',
                  style: const TextStyle(
                    color: MColor.skin,
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          ),
          // Transaction items
          ...log.items.map((item) => _buildTransactionItem(item, isLast: item == log.items.last)),
        ],
      ),
    );
  }

  String _formatDate(String dateStr) {
    try {
      final date = DateTime.parse(dateStr);
      final weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
      final weekday = weekdays[date.weekday - 1];
      return '${date.month}月${date.day}日 $weekday';
    } catch (e) {
      return dateStr;
    }
  }

  Widget _buildTransactionItem(AccountDateLogItem item, {required bool isLast}) {
    final isIncome = item.type == '1';

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: isLast
            ? const BorderRadius.only(
                bottomLeft: Radius.circular(8),
                bottomRight: Radius.circular(8),
              )
            : BorderRadius.zero,
        boxShadow: isLast
            ? [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ]
            : null,
      ),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: isLast
              ? null
              : const Border(
                  bottom: BorderSide(
                    color: Color(0xFFF5F5F5),
                    width: 1,
                  ),
                ),
        ),
        child: Row(
          children: [
            // Category icon
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: MColor.skin.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.home,
                color: MColor.skin,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            // Transaction details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item.categoryName,
                    style: const TextStyle(
                      color: MColor.xFF1B1C1A,
                      fontSize: 15,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (item.memo.isNotEmpty) ...[
                    const SizedBox(height: 4),
                    Text(
                      item.memo,
                      style: const TextStyle(
                        color: MColor.xFF999999,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ],
              ),
            ),
            // Amount and account
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  isIncome ? '+${item.money}' : '-${item.money}',
                  style: TextStyle(
                    color: isIncome ? MColor.xFFCB322E : MColor.skin,
                    fontSize: 17,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  item.bookkeepingName,
                  style: const TextStyle(
                    color: MColor.xFF999999,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
