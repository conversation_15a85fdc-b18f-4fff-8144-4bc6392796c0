import 'package:flutter/material.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/account/account_model.dart';
import 'package:qiazhun/modules/account/account_repo.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_repo.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/widgets/empty_view.dart';
import 'package:qiazhun/widgets/loading_view.dart';

class AccountDetailPage extends StatefulWidget {
  final String accountId;
  const AccountDetailPage({required this.accountId, super.key});

  @override
  State<StatefulWidget> createState() => _AccountDetailState();
}

class _AccountDetailState extends State<AccountDetailPage> {
  bool _isLoading = true;

  final List<AccountModel> _accountList = [];

  @override
  void initState() {
    super.initState();

    AccountRepo.getAccountDetailAndLog(widget.accountId);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBody: true,
      body: SafeArea(
        top: false,
        child: Container(
          color: Colors.yellow,
          child: Stack(
            children: [
              Container(
                color: const Color(0xFFF5F5F5),
              ),
              Positioned(
                // top: 0,
                child: Container(
                  height: 203,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                        colors: [Color(0xFFF5F5F5), Color(0xFFAEE7CD)], begin: Alignment.bottomCenter, end: Alignment.topCenter, tileMode: TileMode.clamp),
                  ),
                ),
              ),
              Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: AppBar(
                      backgroundColor: Colors.transparent,
                      scrolledUnderElevation: 0,
                      centerTitle: true,
                      leading: IconButton(
                        icon: Image.asset(
                          'assets/images/ic_back.png',
                          width: 24,
                          height: 24,
                        ),
                        onPressed: () {
                          RouterHelper.router.pop();
                        },
                      ),
                      titleTextStyle: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                      title: Text(
                        '账户详情',
                        style: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                      ))),
              Positioned.fill(
                  top: AppBar().preferredSize.height + MediaQuery.of(context).padding.top,
                  // top: 0,
                  child: RefreshIndicator(
                    onRefresh: () async {
                      // await _loadData();
                    },
                    child: Builder(builder: (context) {
                      if (_isLoading) {
                        return LoadingView();
                      }
                      if (_accountList.isEmpty) {
                        return EmptyView();
                      }
                      return ListView.separated(
                          padding: EdgeInsets.zero,
                          itemBuilder: (context, index) {
                            return const SizedBox();
                          },
                          separatorBuilder: (context, index) {
                            return const SizedBox(
                              height: 13,
                            );
                          },
                          itemCount: _accountList.length);
                    }),
                  ))
            ],
          ),
        ),
      ),
    );
  }
}
