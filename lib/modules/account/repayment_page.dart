import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/account/account_model.dart';
import 'package:qiazhun/modules/account/account_repo.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:qiazhun/widgets/empty_view.dart';

class RepaymentPage extends StatefulWidget {
  final String accountId;
  const RepaymentPage({required this.accountId, super.key});

  @override
  State<StatefulWidget> createState() => _RepaymentPageState();
}

class _RepaymentPageState extends State<RepaymentPage> {
  bool _isLoading = true;
  AccountModel? _accountData;
  final TextEditingController _amountController = TextEditingController();
  final TextEditingController _memoController = TextEditingController();
  String _selectedRepaymentType = 'full'; // 'full', 'partial', 'minimum'

  @override
  void initState() {
    super.initState();
    _loadAccountData();
  }

  @override
  void dispose() {
    _amountController.dispose();
    _memoController.dispose();
    super.dispose();
  }

  Future<void> _loadAccountData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final response = await AccountRepo.getAccountDetailAndLog(widget.accountId);
      if (response.code == 1 && response.data != null) {
        _accountData = response.data!.detail;
        // Set default amount to bill amount for full repayment
        if (_accountData?.billAmount != null) {
          _amountController.text = _accountData!.billAmount!;
        }
      } else {
        showToast(response.msg ?? '');
      }
    } catch (e) {
      showToast(e.toString());
    } finally {
      _isLoading = false;
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: Image.asset(
            'assets/images/ic_back.png',
            width: 24,
            height: 24,
          ),
          onPressed: () {
            RouterHelper.router.pop();
          },
        ),
        title: const Text(
          '信用卡还款',
          style: TextStyle(
            color: MColor.xFF1B1C1A,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      body: _isLoading ? _buildLoadingView() : _buildContent(),
    );
  }

  Widget _buildLoadingView() {
    return const Center(
      child: CircularProgressIndicator(
        color: MColor.skin,
      ),
    );
  }

  Widget _buildContent() {
    if (_accountData == null) {
      return const Center(
        child: EmptyView(),
      );
    }

    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildAccountCard(),
                const SizedBox(height: 20),
                _buildRepaymentTypeSection(),
                const SizedBox(height: 20),
                _buildAmountSection(),
                const SizedBox(height: 20),
                _buildMemoSection(),
              ],
            ),
          ),
        ),
        _buildBottomButton(),
      ],
    );
  }

  Widget _buildAccountCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFFFED58E), Color(0xFFF6CB86)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.credit_card,
                color: Colors.white,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                '${_accountData!.bankName}信用卡',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          const Text(
            '待还金额',
            style: TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w400,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            '¥${_accountData!.billAmount ?? '0.00'}',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 32,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '还款日',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _accountData!.repaymentDate?.isNotEmpty == true ? '每月${_accountData!.repaymentDate}号' : '未设置',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '可用额度',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '¥${_accountData!.availableAmount ?? '0.00'}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRepaymentTypeSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '还款方式',
            style: TextStyle(
              color: MColor.xFF1B1C1A,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          _buildRepaymentTypeOption('full', '全额还款', _accountData!.billAmount ?? '0.00'),
          const SizedBox(height: 12),
          _buildRepaymentTypeOption('minimum', '最低还款', '${(double.tryParse(_accountData!.billAmount ?? '0') ?? 0) * 0.1}'),
          const SizedBox(height: 12),
          _buildRepaymentTypeOption('partial', '自定义金额', ''),
        ],
      ),
    );
  }

  Widget _buildRepaymentTypeOption(String type, String title, String amount) {
    final isSelected = _selectedRepaymentType == type;
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedRepaymentType = type;
          if (type == 'full') {
            _amountController.text = _accountData!.billAmount ?? '0.00';
          } else if (type == 'minimum') {
            final billAmount = double.tryParse(_accountData!.billAmount ?? '0') ?? 0;
            _amountController.text = (billAmount * 0.1).toStringAsFixed(2);
          } else {
            _amountController.clear();
          }
        });
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? MColor.skin.withValues(alpha: 0.1) : const Color(0xFFF8F8F8),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? MColor.skin : Colors.transparent,
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Icon(
              isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
              color: isSelected ? MColor.skin : MColor.xFF999999,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      color: isSelected ? MColor.xFF1B1C1A : MColor.xFF777777,
                      fontSize: 15,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (amount.isNotEmpty) ...[
                    const SizedBox(height: 4),
                    Text(
                      '¥$amount',
                      style: TextStyle(
                        color: isSelected ? MColor.skin : MColor.xFF999999,
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAmountSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '还款金额',
            style: TextStyle(
              color: MColor.xFF1B1C1A,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _amountController,
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
            ],
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.w600,
              color: MColor.xFF1B1C1A,
            ),
            decoration: InputDecoration(
              hintText: '0.00',
              hintStyle: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.w600,
                color: MColor.xFF999999,
              ),
              prefixText: '¥ ',
              prefixStyle: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.w600,
                color: MColor.xFF1B1C1A,
              ),
              border: InputBorder.none,
              contentPadding: EdgeInsets.zero,
            ),
            enabled: _selectedRepaymentType == 'partial',
          ),
        ],
      ),
    );
  }

  Widget _buildMemoSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '备注',
            style: TextStyle(
              color: MColor.xFF1B1C1A,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _memoController,
            maxLines: 3,
            decoration: InputDecoration(
              hintText: '添加备注信息（可选）',
              hintStyle: TextStyle(
                color: MColor.xFF999999,
                fontSize: 14,
              ),
              border: InputBorder.none,
              contentPadding: EdgeInsets.zero,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomButton() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(color: Color(0xFFF0F0F0), width: 1),
        ),
      ),
      child: SafeArea(
        child: SizedBox(
          width: double.infinity,
          height: 50,
          child: ElevatedButton(
            onPressed: _handleRepayment,
            style: ElevatedButton.styleFrom(
              backgroundColor: MColor.skin,
              foregroundColor: Colors.white,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
            ),
            child: const Text(
              '确认还款',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _handleRepayment() {
    final amount = _amountController.text.trim();
    if (amount.isEmpty || double.tryParse(amount) == null || double.parse(amount) <= 0) {
      showToast('请输入有效的还款金额');
      return;
    }

    // TODO: Implement actual repayment logic
    showToast('还款功能开发中...');

    // For now, just show success and go back
    // RouterHelper.router.pop();
  }
}
