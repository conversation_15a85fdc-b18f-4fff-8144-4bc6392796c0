import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:in_app_purchase_storekit/in_app_purchase_storekit.dart';
import 'package:in_app_purchase_storekit/store_kit_wrappers.dart';
import 'package:qiazhun/common/http.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/main.dart';
import 'package:qiazhun/modules/membership/consumable_store.dart';
import 'package:qiazhun/modules/mine_tab/user_repo.dart';
import 'package:qiazhun/modules/mine_tab/user_store.dart';
import 'package:qiazhun/router/router.dart';
import 'package:fluwx/fluwx.dart';
import 'package:qiazhun/tools/tools.dart';

class MembershipPage extends StatefulWidget {
  const MembershipPage({super.key});

  @override
  State<StatefulWidget> createState() => _MembershipState();
}

class _MembershipState extends State<MembershipPage> {
  bool _selectSvip = true;
  // final List _svipTiles = [
  //   {'title': '永久会员', 'price': '98', 'desc': '永久享用'},
  //   {'title': '年会员', 'price': '28', 'desc': '平均¥9.92元/月'},
  //   {'title': '月会员', 'price': '8', 'desc': '平均¥0.26元/月'},
  // ];

  // final List _vipTiles = [
  //   {'title': '年卡会员', 'price': '15', 'desc': '折合¥0.04元/天'},
  //   {'title': '季卡会员', 'price': '6', 'desc': '折合¥0.06元/天'},
  //   {'title': '月卡会员', 'price': '3', 'desc': '折合¥0.1元/天'},
  // ];

  final List _svipRights = [
    {'title': '广告清除', 'icon': 'assets/images/svip_right_1.png'},
    {'title': '无限账本', 'icon': 'assets/images/svip_right_2.png'},
    {'title': '分类自定', 'icon': 'assets/images/svip_right_3.png'},
    {'title': '搜索特权', 'icon': 'assets/images/svip_right_4.png'},
    {'title': '还款便签', 'icon': 'assets/images/svip_right_8.png'},
    {'title': '消费宝典', 'icon': 'assets/images/svip_right_9.png'},
    {'title': '年费保镖', 'icon': 'assets/images/svip_right_10.png'},
    {'title': '新功能先享', 'icon': 'assets/images/svip_right_7.png'},
  ];

  final List _svipLevels = [];
  final List _vipLevels = [];

  int _selectedTileIndex = 0;
  bool _agreed = false;

  final InAppPurchase _inAppPurchase = InAppPurchase.instance;
  late StreamSubscription<List<PurchaseDetails>> _subscription;
  List<String> _notFoundIds = <String>[];
  List<ProductDetails> _products = <ProductDetails>[];
  List<PurchaseDetails> _purchases = <PurchaseDetails>[];
  List<String> _consumables = <String>[];
  bool _isAvailable = false;
  bool _purchasePending = false;
  bool _loading = true;
  String? _queryProductError;

  @override
  void initState() {
    _initData();
    super.initState();
  }

  Future<void> initStoreInfo(List<String> productIds) async {
    final bool isAvailable = await _inAppPurchase.isAvailable();
    if (!isAvailable) {
      setState(() {
        _isAvailable = isAvailable;
        _products = <ProductDetails>[];
        _purchases = <PurchaseDetails>[];
        _notFoundIds = <String>[];
        _consumables = <String>[];
        _purchasePending = false;
        _loading = false;
      });
      return;
    }

    if (Platform.isIOS) {
      final InAppPurchaseStoreKitPlatformAddition iosPlatformAddition = _inAppPurchase.getPlatformAddition<InAppPurchaseStoreKitPlatformAddition>();
      await iosPlatformAddition.setDelegate(ExamplePaymentQueueDelegate());
    }

    final ProductDetailsResponse productDetailResponse = await _inAppPurchase.queryProductDetails(productIds.toSet());
    if (productDetailResponse.error != null) {
      setState(() {
        _queryProductError = productDetailResponse.error!.message;
        _isAvailable = isAvailable;
        _products = productDetailResponse.productDetails;
        _purchases = <PurchaseDetails>[];
        _notFoundIds = productDetailResponse.notFoundIDs;
        _consumables = <String>[];
        _purchasePending = false;
        _loading = false;
      });
      return;
    }

    if (productDetailResponse.productDetails.isEmpty) {
      setState(() {
        _queryProductError = null;
        _isAvailable = isAvailable;
        _products = productDetailResponse.productDetails;
        _purchases = <PurchaseDetails>[];
        _notFoundIds = productDetailResponse.notFoundIDs;
        _consumables = <String>[];
        _purchasePending = false;
        _loading = false;
      });
      return;
    }

    final List<String> consumables = await ConsumableStore.load();
    setState(() {
      _isAvailable = isAvailable;
      _products = productDetailResponse.productDetails;
      _notFoundIds = productDetailResponse.notFoundIDs;
      _consumables = consumables;
      _purchasePending = false;
      _loading = false;
    });
  }

  Future<void> _initData() async {
    Loading.show();
    try {
      var resp = await UserRepo.getVipLevel();
      if (resp.code == 1) {
        _svipLevels.clear();
        _svipLevels.addAll(resp.data['svip']);
        _vipLevels.clear();
        _vipLevels.addAll(resp.data['vip']);
        if (Platform.isIOS) {
          List<String> productIds = [];
          _svipLevels.forEach((el) => productIds.add(el['appleId']));
          _vipLevels.forEach((el) => productIds.add(el['appleId']));

          final Stream<List<PurchaseDetails>> purchaseUpdated = _inAppPurchase.purchaseStream;
          _subscription = purchaseUpdated.listen((List<PurchaseDetails> purchaseDetailsList) {
            _listenToPurchaseUpdated(purchaseDetailsList);
          }, onDone: () {
            _subscription.cancel();
          }, onError: (Object error) {
            // handle error here.
          });
          await initStoreInfo(productIds);
        }
        setState(() {});
      } else {
        showToast(resp.msg ?? '获取VIP等级失败');
      }
    } catch (e) {
      showToast('获取VIP等级失败 $e');
    } finally {
      Loading.dismiss();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: Theme.of(context).copyWith(
        dividerTheme: DividerThemeData(
          color: Colors.transparent,
        ),
      ),
      child: Scaffold(
        extendBody: true,
        resizeToAvoidBottomInset: false,
        persistentFooterButtons: [_bottomView],
        body: SafeArea(
          // maintainBottomViewPadding: true,
          top: false,
          child: Stack(
            children: [
              Container(
                color: const Color(0xFFF5F5F5),
              ),
              Positioned(
                // top: 0,
                child: Image.asset(
                  'assets/images/membership_bg.png',
                  width: MediaQuery.of(context).size.width,
                  fit: BoxFit.fill,
                ),
              ),
              Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: AppBar(
                      backgroundColor: Colors.transparent,
                      centerTitle: true,
                      leading: IconButton(
                        icon: Image.asset(
                          'assets/images/ic_back.png',
                          width: 24,
                          height: 24,
                        ),
                        onPressed: () {
                          RouterHelper.router.pop();
                        },
                      ),
                      titleTextStyle: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                      title: Container(
                        height: 36,
                        width: 184,
                        padding: EdgeInsets.all(2),
                        decoration: BoxDecoration(color: MColor.xFFFFFFFF, borderRadius: BorderRadius.circular(18)),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Expanded(
                              child: GestureDetector(
                                onTap: () {
                                  if (!_selectSvip) {
                                    setState(() {
                                      _selectSvip = true;
                                      _selectedTileIndex = 0;
                                    });
                                  }
                                },
                                child: Container(
                                  width: 90,
                                  decoration: BoxDecoration(color: _selectSvip ? MColor.xFFFFEBC4 : MColor.xFFFFFFFF, borderRadius: BorderRadius.circular(22)),
                                  child: Center(
                                    child: Text(
                                      'SVIP会员',
                                      style: TextStyle(height: 1.4, fontSize: 16, color: _selectSvip ? MColor.xFF895C23 : MColor.xFF999999),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            Expanded(
                              child: GestureDetector(
                                onTap: () {
                                  if (_selectSvip) {
                                    setState(() {
                                      _selectSvip = false;
                                      _selectedTileIndex = 0;
                                    });
                                  }
                                },
                                child: Container(
                                  width: 90,
                                  decoration: BoxDecoration(color: !_selectSvip ? MColor.xFFFFEBC4 : MColor.xFFFFFFFF, borderRadius: BorderRadius.circular(22)),
                                  child: Center(
                                    child: Text(
                                      'VIP会员',
                                      style: TextStyle(height: 1.4, fontSize: 16, color: !_selectSvip ? MColor.xFF895C23 : MColor.xFF999999),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ))),
              Positioned.fill(
                top: AppBar().preferredSize.height + MediaQuery.of(context).padding.top,
                // top: 0,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 14.0),
                  child: Column(
                    children: [
                      const SizedBox(
                        height: 14,
                      ),
                      _selectSvip ? _svipContentView : _vipContentView,
                    ],
                  ),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget get _svipContentView {
    return Column(
      children: [
        Container(
          width: 319,
          height: 133,
          padding: EdgeInsets.fromLTRB(14, 0, 14, 0),
          decoration: BoxDecoration(
              image: DecorationImage(
            image: AssetImage('assets/images/svip_bg.png'),
            fit: BoxFit.cover,
          )),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(
                    height: 13,
                  ),
                  Text(
                    '大鳄专享会员',
                    style: TextStyle(height: 1.4, fontSize: 16, fontWeight: FontWeight.bold, color: MColor.xFFF5CDA8),
                  ),
                  const SizedBox(
                    height: 11,
                  ),
                  Text(
                    '超实用尊贵特权，年节省1000+元。',
                    style: TextStyle(height: 1.4, fontSize: 14, color: MColor.xFFF5CDA8),
                  ),
                  const SizedBox(
                    height: 14,
                  ),
                  Row(
                    children: [
                      Image.asset(
                        'assets/images/svip_bg_people.png',
                        width: 72,
                        height: 33,
                        fit: BoxFit.contain,
                      ),
                      const SizedBox(
                        width: 6,
                      ),
                      Padding(
                        padding: const EdgeInsets.only(bottom: 8.0),
                        child: Text(
                          '30W+人已经开通会员',
                          style: TextStyle(height: 1.4, fontSize: 12, color: MColor.xFFFFFFFF),
                        ),
                      ),
                    ],
                  )
                ],
              ),
              Image.asset(
                'assets/images/svip_bg_icon.png',
                width: 59,
                fit: BoxFit.fitWidth,
              )
            ],
          ),
        ),
        const SizedBox(
          height: 12,
        ),
        Text(
          '8大特权，记好账超方便！',
          style: TextStyle(height: 1.4, fontSize: 16, fontWeight: FontWeight.bold, color: MColor.xFF1B1C1A),
        ),
        const SizedBox(
          height: 8,
        ),
        GridView.builder(
            padding: EdgeInsets.zero,
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(crossAxisCount: 4, crossAxisSpacing: 0, mainAxisSpacing: 0, childAspectRatio: 1.2),
            itemCount: _svipRights.length,
            itemBuilder: (context, index) {
              return Container(
                // color: Colors.yellow,
                padding: EdgeInsets.all(10),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(
                      _svipRights[index]['icon'],
                      width: 26,
                      height: 26,
                      fit: BoxFit.cover,
                    ),
                    SizedBox(height: 4),
                    Text(
                      _svipRights[index]['title'].toString(),
                      style: TextStyle(fontSize: 12, height: 1.4, color: MColor.xFF1B1C1A),
                    ),
                  ],
                ),
              );
            }),
        const SizedBox(
          height: 18,
        ),
        if (_svipLevels.length >= 3)
          Row(children: [
            _tileView(0, _svipLevels[0]),
            const SizedBox(width: 16),
            _tileView(1, _svipLevels[1]),
            const SizedBox(width: 16),
            _tileView(2, _svipLevels[2])
          ])
      ],
    );
  }

  Widget get _vipContentView {
    return Column(
      children: [
        Image.asset(
          'assets/images/vip_bg.png',
          width: 319,
          height: 133,
        ),
        const SizedBox(
          height: 7,
        ),
        Text(
          '屏蔽广告，记账有爽感',
          style: TextStyle(height: 1.4, fontSize: 16, fontWeight: FontWeight.bold, color: MColor.xFF1B1C1A),
        ),
        const SizedBox(
          height: 18,
        ),
        Image.asset(
          'assets/images/vip_intro.png',
          width: 347,
          height: 93,
          fit: BoxFit.fill,
        ),
        const SizedBox(
          height: 18,
        ),
        if (_vipLevels.length >= 3)
          Row(children: [
            _tileView(0, _vipLevels[0]),
            const SizedBox(width: 16),
            _tileView(1, _vipLevels[1]),
            const SizedBox(width: 16),
            _tileView(2, _vipLevels[2])
          ])
      ],
    );
  }

  Widget _tileView(int index, dynamic tile) {
    return Expanded(
      child: GestureDetector(
        onTap: () {
          if (_selectedTileIndex != index) {
            setState(() {
              _selectedTileIndex = index;
            });
          }
        },
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 14),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: _selectedTileIndex == index ? MColor.xFFD6AC94 : MColor.xFF000000.withOpacity(0.06), width: 1)),
          child: Column(
            children: [
              Text(
                tile['name'],
                style: TextStyle(height: 1.4, fontSize: 14, color: _selectedTileIndex == index ? MColor.xFFAE6C49 : MColor.xFF1B1C1A),
              ),
              const SizedBox(
                height: 14,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  RichText(
                    textAlign: TextAlign.center,
                    text: WidgetSpan(
                        child: Row(
                      children: [
                        Text('¥', style: TextStyle(color: MColor.xFFAE6C49, fontSize: 14, fontWeight: FontWeight.bold)),
                        const SizedBox(
                          width: 2,
                        ),
                        Text(
                          tile['price'],
                          style: TextStyle(color: MColor.xFFAE6C49, fontSize: 18, fontWeight: FontWeight.bold),
                        ),
                      ],
                    )),
                  ),
                ],
              ),
              const SizedBox(
                height: 14,
              ),
              Text(
                tile['desc'],
                style: TextStyle(height: 1.4, fontSize: 12, color: _selectedTileIndex == index ? MColor.xFFAE6C49 : MColor.xFF999999),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _submit() async {
    Loading.show();
    try {
      var resp = await UserRepo.membershipOrder(_selectSvip ? _svipLevels[_selectedTileIndex]['id'] : _vipLevels[_selectedTileIndex]['id']);
      if (resp.code == 1) {
        resp.data[''];
        final Fluwx fluwx = Fluwx();
        fluwx
            .pay(
                which: Payment(
          appId: 'wxfa8f65bc326d6783',
          partnerId: resp.data['partnerid'].toString(),
          prepayId: resp.data['prepayid'].toString(),
          packageValue: 'WXPay',
          nonceStr: resp.data['noncestr'].toString(),
          timestamp: int.tryParse(resp.data['timestamp']) ?? 0,
          sign: resp.data['sign'].toString(),
        ))
            .then((result) {
          showToast('开通会员 ${result ? '成功' : '失败'}');
          if (result) {
            UserStore.to.getUserInfo();
          }
        });
      } else {
        showToast(resp.msg ?? '创建订单失败');
      }
    } catch (e) {
      showToast('创建订单失败 $e');
    } finally {
      Loading.dismiss();
    }
  }

  Future<void> _listenToPurchaseUpdated(List<PurchaseDetails> purchaseDetailsList) async {
    for (final PurchaseDetails purchaseDetails in purchaseDetailsList) {
      if (purchaseDetails.status == PurchaseStatus.pending) {
        Loading.show();
      } else {
        if (purchaseDetails.status == PurchaseStatus.error) {
          showToast('Apple支付失败 ${purchaseDetails.error}');
          Loading.dismiss();
        } else if (purchaseDetails.status == PurchaseStatus.purchased || purchaseDetails.status == PurchaseStatus.restored) {
          await _payWithApple(purchaseDetails, purchaseDetails.verificationData.serverVerificationData);
        }
        if (purchaseDetails.pendingCompletePurchase) {
          await _inAppPurchase.completePurchase(purchaseDetails);
        }
      }
    }
  }

  Future<void> deliverProduct(PurchaseDetails purchaseDetails) async {
    // IMPORTANT!! Always verify purchase details before delivering the product.
    // if (purchaseDetails.productID == _kConsumableId) {
    //   await ConsumableStore.save(purchaseDetails.purchaseID!);
    //   final List<String> consumables = await ConsumableStore.load();
    //   setState(() {
    //     _purchasePending = false;
    //     _consumables = consumables;
    //   });
    // } else {
    //   setState(() {
    //     _purchases.add(purchaseDetails);
    //     _purchasePending = false;
    //   });
    // }
  }

  Future<void> _payWithApple(PurchaseDetails purchaseDetails, String verificationData) async {
    Loading.show();
    try {
      var resp = await UserRepo.payWithApple(verificationData);
      if (resp.code == 1) {
        unawaited(deliverProduct(purchaseDetails));
      } else {
        showToast('Apple支付失败2');
        return;
      }
    } catch (e) {
    } finally {
      Loading.dismiss();
    }
  }

  Widget get _bottomView {
    var price = '';
    if (_svipLevels.isNotEmpty && _vipLevels.isNotEmpty) {
      price = _selectSvip ? _svipLevels[_selectedTileIndex]['price'] : _vipLevels[_selectedTileIndex]['price'];
    }
    return Column(
      children: [
        GestureDetector(
          onTap: () async {
            var productId = _selectSvip ? _svipLevels[_selectedTileIndex]['appleId'] : _vipLevels[_selectedTileIndex]['appleId'];
            var productDetails = _products.firstWhereOrNull((el) => el.id == productId);
            if (!_agreed) {
              showCustomDialog(
                '确认支付前请确认已经阅读并同意《会员服务协议》与《自动续费管理规则》',
                onConfirm: () async {
                  setState(() {
                    _agreed = true;
                  });

                  if (Platform.isIOS) {
                    var purchaseParam = PurchaseParam(
                      productDetails: productDetails!,
                    );
                    _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);
                  } else {
                    _submit();
                  }
                },
                okTitle: '确认',
                cancel: true,
                onCancel: () {},
              );
            } else {
              if (Platform.isIOS) {
                // _payWithApple(purchaseDetails, verificationData)
                var purchaseParam = PurchaseParam(
                  productDetails: productDetails!,
                );
                _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);
              } else {
                _submit();
              }
            }

            // _submit();
          },
          child: Container(
            height: 50,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(25),
                gradient: LinearGradient(
                    begin: const FractionalOffset(0.0, 0.0), end: const FractionalOffset(0.0, 1.0), colors: [Color(0xFF3F4150), Color(0xFF1C1C2A)])),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  '立即支付 ¥ ${price}',
                  style: TextStyle(color: MColor.xFFFFFFFF, fontWeight: FontWeight.w500, fontSize: 16, height: 1),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(
          height: 14,
        ),
        GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () {
            setState(() {
              _agreed = !_agreed;
            });
          },
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              RichText(
                  textAlign: TextAlign.center,
                  text: WidgetSpan(
                      child: Row(
                    children: [
                      Icon(
                        _agreed ? Icons.check_circle : Icons.radio_button_unchecked,
                        size: 14,
                        color: MColor.xFFF6CB86,
                      ),
                      const SizedBox(
                        width: 2,
                      ),
                      Text('开通前请确认', style: TextStyle(height: 1, fontSize: 12, color: MColor.xFF777777)),
                      GestureDetector(
                          onTap: () {
                            RouterHelper.router
                                .pushNamed(Routes.webPath, extra: {'url': '${HttpUtil.kBaseUrl}/index/index/serviceAgreement', 'title': '会员服务协议'});
                          },
                          child: Text('《会员服务协议》', style: TextStyle(height: 1, fontSize: 12, color: MColor.skin))),
                      Text('与', style: TextStyle(height: 1, fontSize: 12, color: MColor.xFF777777)),
                      GestureDetector(
                          onTap: () {
                            RouterHelper.router
                                .pushNamed(Routes.webPath, extra: {'url': '${HttpUtil.kBaseUrl}/index/index/managementRules', 'title': '自动续费管理规则'});
                          },
                          child: Text('《自动续费管理规则》', style: TextStyle(height: 1, fontSize: 12, color: MColor.skin)))
                    ],
                  ))),
            ],
          ),
        ),
        const SizedBox(
          height: 16,
        )
      ],
    );
  }
}

/// Example implementation of the
/// [`SKPaymentQueueDelegate`](https://developer.apple.com/documentation/storekit/skpaymentqueuedelegate?language=objc).
///
/// The payment queue delegate can be implementated to provide information
/// needed to complete transactions.
class ExamplePaymentQueueDelegate implements SKPaymentQueueDelegateWrapper {
  @override
  bool shouldContinueTransaction(SKPaymentTransactionWrapper transaction, SKStorefrontWrapper storefront) {
    return true;
  }

  @override
  bool shouldShowPriceConsent() {
    return false;
  }
}
