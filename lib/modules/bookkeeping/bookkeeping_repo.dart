import 'dart:convert';

import 'package:qiazhun/common/base_model.dart';
import 'package:qiazhun/common/http.dart';
import 'package:qiazhun/modules/account/account_model.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_model.dart';

class BookkeepingRepo {
  static Future<BaseModel<dynamic>> addBookkeeping(String name, bool isJoinTotal, String memo, String icon) async {
    var resp = await HttpUtil()
        .post('api/bookkeeping/addBookkeeping', data: {'accountBookName': name, 'isJoinTotal': isJoinTotal ? '1' : '2', 'memo': memo, 'icon': icon});
    return BaseModel.fromJson(resp, (json) => json);
  }

  static Future<BaseModel<dynamic>> deleteBill(String moneyLogId) async {
    var resp = await HttpUtil().post('api/bookkeeping/deleteBill', data: {'moneylogId': moneyLogId});
    return BaseModel.fromJson(resp, (json) => json);
  }

  static Future<BaseModel<List<BookkeepingInfo>>> getBookkeepingList() async {
    var resp = await HttpUtil().post('api/bookkeeping/getBookkeepingList');
    return BaseModel.fromJson(resp, (json) => (json as List<dynamic>).map((dynamic e) => BookkeepingInfo.fromJson(e)).toList());
  }

  static Future<BaseModel<dynamic>> delBookkeeping(String bookkeepingNumber) async {
    var resp = await HttpUtil().post('api/bookkeeping/delBookkeeping', data: {'bookkeepingNumber': bookkeepingNumber});
    return BaseModel.fromJson(resp, (json) => json);
  }

  static Future<BaseModel<dynamic>> sortBookkeeping(String sortedId) async {
    var resp = await HttpUtil().post('api/bookkeeping/updateBookkeepingSort', data: {'sortedId': sortedId});
    return BaseModel.fromJson(resp, (json) => json);
  }

  static Future<BaseModel<dynamic>> delBookkeepingCategory(String bookkeepingCategoryId) async {
    var resp = await HttpUtil().post('api/bookkeeping/deleteBookkeepingCategory', data: {'bookkeepingCategoryId': bookkeepingCategoryId});
    return BaseModel.fromJson(resp, (json) => json);
  }

  static Future<BaseModel<dynamic>> sortBookkeepingCategory(String type, String idList) async {
    var resp = await HttpUtil().post('api/bookkeeping/updateBookkeepingCategorySort', data: {'categoryType': type, 'sortedId': idList});
    return BaseModel.fromJson(resp, (json) => json);
  }

  static Future<BaseModel<dynamic>> addBookkeepingCategory(num pid, String icon, String name, String categoryType) async {
    var resp = await HttpUtil().post('api/bookkeeping/addBookkeepingCategory',
        data: {'pid': pid.toString(), 'icon': icon, 'bookkeepingCategoryName': name, 'categoryType': categoryType});
    return BaseModel.fromJson(resp, (json) => json);
  }

  static Future<BaseModel<BookkeepingCategoryResp>> getBookkeepingCategory() async {
    var resp = await HttpUtil().post('api/bookkeeping/getBookkeepingCategory', data: {});
    return BaseModel.fromJson(resp, (json) => BookkeepingCategoryResp.fromJson(json));
  }

  static Future<BaseModel<dynamic>> addOfficialCategory(String bookkeepingCategoryId) async {
    var resp = await HttpUtil().post('api/bookkeeping/addOfficialCategory', data: {'bookkeepingCategoryId': bookkeepingCategoryId});
    return BaseModel.fromJson(resp, (json) => json);
  }

  static Future<BaseModel<List<CategoryItem>>> getBookkeepingCategoryPage(String categoryType) async {
    var resp = await HttpUtil().post('api/bookkeeping/getBookkeepingCategoryPage', data: {'categoryType': categoryType});
    return BaseModel.fromJson(resp, (json) => (json as List<dynamic>).map((dynamic e) => CategoryItem.fromJson(e)).toList());
  }

  static Future<BaseModel<List<AccountModel>>> getAccountList({required int? page, required int? pageCount}) async {
    var resp = await HttpUtil().post('api/card/list', data: {'page': page, 'pageCount': pageCount});
    return BaseModel.fromJson(resp, (json) => (json as List<dynamic>).map((dynamic e) => AccountModel.fromJson(e)).toList());
  }

  static Future<BaseModel<BillDetailInfo>> billDetail(String moneyLogId) async {
    var resp = await HttpUtil().post('api/bookkeeping/billDetail', data: {'moneyLogId': moneyLogId});
    return BaseModel.fromJson(resp, (json) => BillDetailInfo.fromJson(json));
  }

  static Future<BaseModel<dynamic>> addBill(
      {required String bookkeepingNumber,
      required int categoryId,
      required String action,
      required String money,
      required String isSave,
      String? isSaveMoney,
      required String isNecessaryStatus,
      required String nowTime,
      required int accountId,
      String? memo,
      String? moneyLogId}) async {
    var data = {
      'bookkeepingNumber': bookkeepingNumber,
      'categoryId': '$categoryId',
      'memo': memo,
      'action': action,
      'money': money,
      'isSave': isSave,
      'isNecessaryStatus': isNecessaryStatus,
      'nowTime': nowTime,
      'accountId': accountId
    };
    if (isSaveMoney?.isNotEmpty == true) {
      data['isSaveMoney'] = isSaveMoney!;
    }
    if (moneyLogId?.isNotEmpty == true) {
      data['moneyLogId'] = moneyLogId;
    }
    var resp = await HttpUtil().post('api/bookkeeping/addBill', data: data);
    return BaseModel.fromJson(resp, (json) => json);
  }
}
