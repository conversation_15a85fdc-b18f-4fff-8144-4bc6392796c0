import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:intl/intl.dart';
import 'package:qiazhun/common/loading.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/models/home_detail_model.dart';
import 'package:qiazhun/modules/detail_tab/detail_repo.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:qiazhun/widgets/date_range_view.dart';
import 'package:qiazhun/widgets/empty_view.dart';
import 'package:qiazhun/widgets/loading_view.dart';
import 'package:qiazhun/widgets/progress_view.dart';
import 'package:qiazhun/widgets/round_image.dart';

class CategoryWaterFlowPage extends StatefulWidget {
  final String title;
  final int pageType; // 类型:1=收入 2=支出 3=全部
  final int dateRangeType; //0 日 1 本周 2 本月 3 本年 4 自定义
  final String categoryId;
  final DateTime dateBegin;
  final DateTime dateEnd;
  const CategoryWaterFlowPage(
      {required this.title,
      required this.pageType,
      required this.dateRangeType,
      required this.dateBegin,
      required this.dateEnd,
      required this.categoryId,
      super.key});

  @override
  State<StatefulWidget> createState() => _CategoryWaterFlowState();
}

class _CategoryWaterFlowState extends State<CategoryWaterFlowPage> {
  bool _isLoading = true;

  bool _sortByAmount = true;

  List<CategoryFlowLog> _flowLogList = [];

  late int _dateRangeType = 0;
  DateTime _dateTimeStart = DateTime.now().subtract(Duration(days: DateTime.now().weekday - 1));
  DateTime _dateTimeEnd = DateTime.now();

  @override
  void initState() {
    super.initState();
    _dateRangeType = widget.dateRangeType;
    _dateTimeStart = widget.dateBegin;
    _dateTimeEnd = widget.dateEnd;
    _loadData();
  }

  Future<void> _loadData() async {
    _isLoading = true;
    try {
      var resp = await DetailRepo.getCategoryMoneyLog(
          timeInterval: generateDateRange2(_dateTimeStart, _dateTimeEnd), order: _sortByAmount ? '1' : '2', categoryId: widget.categoryId);
      if (resp.code == 1) {
        _flowLogList.clear();
        _flowLogList.addAll(resp.data ?? []);
      } else {
        showToast(resp.msg ?? '');
      }
    } catch (e) {
      showToast(e.toString());
    } finally {}
    _isLoading = false;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBody: true,
      body: SafeArea(
        top: false,
        child: Container(
          color: Colors.yellow,
          child: Stack(
            children: [
              Container(
                color: const Color(0xFFF5F5F5),
              ),
              Positioned(
                // top: 0,
                child: Container(
                  height: 203,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                        colors: [Color(0xFFF5F5F5), Color(0xFFAEE7CD)], begin: Alignment.bottomCenter, end: Alignment.topCenter, tileMode: TileMode.clamp),
                  ),
                ),
              ),
              Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: AppBar(
                      backgroundColor: Colors.transparent,
                      scrolledUnderElevation: 0,
                      centerTitle: true,
                      leading: IconButton(
                        icon: Image.asset(
                          'assets/images/ic_back.png',
                          width: 24,
                          height: 24,
                        ),
                        onPressed: () {
                          RouterHelper.router.pop();
                        },
                      ),
                      titleTextStyle: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                      title: Text(
                        widget.title,
                        style: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                      ))),
              Positioned.fill(
                  top: AppBar().preferredSize.height + MediaQuery.of(context).padding.top,
                  // top: 0,
                  child: Builder(builder: (context) {
                    if (_isLoading) {
                      return LoadingView();
                    }
                    if (_flowLogList == null) {
                      return EmptyView();
                    }
                    return Column(
                      children: [
                        Expanded(
                          child: ListView.separated(
                              padding: EdgeInsets.zero,
                              itemBuilder: (context, index) {
                                if (index == 0) {
                                  return _headerViewAll;
                                } else if (index == 1 && _flowLogList.isEmpty == true) {
                                  return EmptyView();
                                }
                                return Container(color: MColor.xFFF5F5F5, child: _MoneyFlowItemView(_flowLogList[index - 1]));
                              },
                              separatorBuilder: (context, index) {
                                return Divider(
                                  height: 0.5,
                                  thickness: 0.5,
                                  color: MColor.xFFD9D9D9,
                                  indent: 15,
                                );
                              },
                              itemCount: (_flowLogList.length ?? 0) + 1),
                        )
                      ],
                    );
                  }))
            ],
          ),
        ),
      ),
    );
  }

  Widget get _headerViewAll {
    return Column(
      children: [
        DateRangeView(
            dateRangeType: _dateRangeType,
            dateTimeStart: _dateTimeStart,
            dateTimeEnd: _dateTimeEnd,
            showQuickSelect: true,
            onDateRangeChanged: (type, start, end) {
              _dateRangeType = type;
              _dateTimeStart = start;
              _dateTimeEnd = end;
              _loadData();
            }),
        const SizedBox(height: 12),
        _sortView,
      ],
    );
  }

  Widget get _sortView {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 14, vertical: 4),
      // decoration: BoxDecoration(color: MColor.skin),
      child: Row(
        children: [
          // Text(
          //   _headerTitle,
          //   style: TextStyle(fontSize: 14, height: 1.4, fontWeight: FontWeight.w500, color: MColor.xFF1B1C1A),
          // ),
          const Spacer(),
          Container(
              padding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
              decoration: BoxDecoration(
                color: MColor.xFFEEEEEE,
                borderRadius: BorderRadius.circular(15),
              ),
              child: Row(
                children: [
                  GestureDetector(
                    onTap: () {
                      if (!_sortByAmount) {
                        _sortByAmount = true;
                        _loadData();
                      }
                    },
                    child: Container(
                        height: 20,
                        padding: EdgeInsets.symmetric(horizontal: 6),
                        alignment: Alignment.center,
                        decoration: _sortByAmount ? BoxDecoration(borderRadius: BorderRadius.circular(14), color: MColor.xFFFFFFFF) : null,
                        child: Text(
                          '按金额',
                          style: TextStyle(fontSize: 12, height: 1.4, color: MColor.xFF1B1C1A),
                        )),
                  ),
                  const SizedBox(width: 8),
                  GestureDetector(
                    onTap: () {
                      if (_sortByAmount) {
                        _sortByAmount = false;
                        _loadData();
                      }
                    },
                    child: Container(
                        height: 20,
                        padding: EdgeInsets.symmetric(horizontal: 6),
                        alignment: Alignment.center,
                        decoration: !_sortByAmount ? BoxDecoration(borderRadius: BorderRadius.circular(14), color: MColor.xFFFFFFFF) : null,
                        child: Text(
                          '按时间',
                          style: TextStyle(fontSize: 12, height: 1.4, color: MColor.xFF1B1C1A),
                        )),
                  )
                ],
              ))
        ],
      ),
    );
  }
}

class _MoneyFlowItemView extends StatelessWidget {
  final CategoryFlowLog data;
  const _MoneyFlowItemView(this.data, {super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
        color: MColor.xFFF5F5F5,
        padding: const EdgeInsets.symmetric(vertical: 10),
        child: Row(children: [
          const SizedBox(width: 14),
          Container(
            width: 36,
            height: 36,
            decoration: BoxDecoration(
              color: MColor.xFFFED58E,
              borderRadius: BorderRadius.circular(18),
            ),
            child: RoundImage(imageUrl: data.icon ?? '', radius: 18, size: 36),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Row(
                        children: [
                          Expanded(
                            child: Text(
                              data.memo,
                              overflow: TextOverflow.ellipsis,
                              softWrap: true,
                              maxLines: 1,
                              style: TextStyle(fontSize: 15, height: 1.4, fontWeight: FontWeight.w500, color: MColor.xFF1B1C1A),
                            ),
                          ),
                          const SizedBox(width: 6),
                          Text(
                            data.percent ?? '',
                            softWrap: false,
                            style: TextStyle(fontSize: 12, height: 1.4, color: MColor.xFF1B1C1A),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      data.money ?? '',
                      textAlign: TextAlign.end,
                      style: TextStyle(fontSize: 14, height: 1.4, fontWeight: FontWeight.w500, color: MColor.xFF1B1C1A),
                    ),
                  ],
                ),
                const SizedBox(height: 6),
                Builder(builder: (context) {
                  var fullWidth = (MediaQuery.of(context).size.width - 14 * 2 - 36 - 10);
                  var ratio = (double.tryParse(data.actualPercentage ?? '') ?? 0.0) / 100;
                  return Container(
                    width: fullWidth,
                    height: 6,
                    decoration: BoxDecoration(
                      color: MColor.xFFEEEEEE,
                      borderRadius: BorderRadius.circular(3),
                    ),
                    child: Row(
                      children: [
                        Container(
                          width: fullWidth * ratio,
                          height: 6,
                          decoration: BoxDecoration(
                            color: MColor.xFFFED58E,
                            borderRadius: BorderRadius.circular(3),
                          ),
                        ),
                      ],
                    ),
                  );
                }),
                const SizedBox(height: 6),
                Text(
                  data.createtime ?? '',
                  style: TextStyle(fontSize: 12, height: 1.4, fontWeight: FontWeight.w400, color: MColor.xFF999999),
                )
              ],
            ),
          ),
          const SizedBox(width: 14),
        ]));
  }
}
